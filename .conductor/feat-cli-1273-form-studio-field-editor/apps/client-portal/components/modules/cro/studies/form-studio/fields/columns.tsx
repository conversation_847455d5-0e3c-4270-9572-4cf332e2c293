"use client";
import type { ColumnDef } from "@tanstack/react-table";
import { <PERSON><PERSON>, Pencil, Trash2, View } from "lucide-react";

import { Tooltip } from "@/components/ui/tooltip";
import { useRole } from "@/hooks/auth";
import { UserType } from "@/lib/apis/auth/types";
import type {
  FieldLibraryItem,
  FieldStatus,
} from "@/lib/apis/field-library/types";

import { CompliantBadge } from "./components/compliant-badge";
import { FieldStatusBadge } from "./components/field-status-badge";

const statusOrder = {
  DRAFT: 0,
  PUBLISHED: 1,
  ARCHIVED: 2,
};

export const getColumns = (opts: {
  onEdit: (id: string) => void;
}): ColumnDef<FieldLibraryItem>[] => [
  {
    header: "Field Label",
    accessorKey: "displayLabel",
  },
  {
    header: "Shortcode",
    accessorKey: "shortCode",
  },
  {
    header: "Datatype",
    accessorKey: "fieldType",
    cell: ({ getValue }) => {
      const value = getValue() as string;
      return (
        <span className="whitespace-nowrap rounded-full bg-gray-100 px-2 py-0.5 text-xs font-medium text-[#667085]">
          {value}
        </span>
      );
    },
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ getValue }) => {
      const status = getValue() as FieldStatus;
      return <FieldStatusBadge status={status} />;
    },
    sortingFn: (rowA, rowB, columnId) => {
      const valueA = rowA.getValue(columnId) as keyof typeof statusOrder;
      const valueB = rowB.getValue(columnId) as keyof typeof statusOrder;
      return statusOrder[valueA] - statusOrder[valueB];
    },
  },
  {
    header: "Compliant",
    id: "compliant",
    cell: ({ row }) => {
      const isCompliant = !!row.original.metadata?.cdash;
      const isSdtm = !!row.original.metadata?.sdtm;
      return (
        <CompliantBadge
          domain={isCompliant ? "CDASH" : isSdtm ? "SDTM" : "Custom"}
        />
      );
    },
  },
  {
    header: "Actions",
    id: "actions",
    cell: function ActionsCell({ row }) {
      const { is } = useRole();
      const isCRO = is(UserType.SPONSOR);

      const handleEdit = () => {
        opts.onEdit(row.original.id);
      };

      const handleDelete = () => {
        console.log("Delete field:", row.original.id);
      };

      const handleDuplicate = () => {
        console.log("Duplicate field:", row.original.id);
      };

      return (
        <div className="flex items-center gap-2.5">
          <Tooltip content="View">
            <View
              aria-label="View"
              aria-description="View field details"
              size={20}
              className="cursor-pointer text-gray-500 hover:text-gray-700"
            />
          </Tooltip>

          {isCRO && (
            <>
              <Tooltip content="Edit">
                <Pencil
                  aria-label="Edit"
                  aria-description="Edit field"
                  size={20}
                  className="cursor-pointer text-gray-500 hover:text-gray-700"
                  onClick={handleEdit}
                />
              </Tooltip>

              <Tooltip content="Duplicate">
                <Copy
                  aria-label="Duplicate"
                  aria-description="Duplicate field"
                  size={20}
                  className="cursor-pointer text-gray-500 hover:text-gray-700"
                  onClick={handleDuplicate}
                />
              </Tooltip>

              <Tooltip content="Delete">
                <Trash2
                  aria-label="Delete"
                  aria-description="Delete field"
                  size={20}
                  className="cursor-pointer text-gray-500 hover:text-red-700"
                  onClick={handleDelete}
                />
              </Tooltip>
            </>
          )}
        </div>
      );
    },
    enableSorting: false,
  },
];
