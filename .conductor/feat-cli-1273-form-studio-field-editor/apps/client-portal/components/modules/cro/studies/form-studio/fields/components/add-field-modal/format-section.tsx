"use client";

import { useFormContext, useWatch } from "react-hook-form";

import { Label } from "@/components/ui/form";
import { InputField } from "@/components/ui/form/input";
import { Select } from "@/components/ui/form/select";
import { SwitchField } from "@/components/ui/form/switch";

const dateFormatOptions = [
  { label: "MM/DD/YYYY", value: "MM/DD/YYYY" },
  { label: "DD/MM/YYYY", value: "DD/MM/YYYY" },
  { label: "YYYY/MM/DD", value: "YYYY/MM/DD" },
];

const dateTimeFormatOptions = [
  { label: "MM/DD/YYYY HH:mm", value: "MM/DD/YYYY HH:mm" },
  { label: "DD/MM/YYYY HH:mm", value: "DD/MM/YYYY HH:mm" },
  { label: "YYYY/MM/DD HH:mm", value: "YYYY/MM/DD HH:mm" },
];

export const FormatSection = () => {
  const { control } = useFormContext();
  const fieldType = useWatch({ control, name: "fieldType" });

  // Don't render the section if no field type is selected or if it's Codelist
  if (!fieldType || fieldType === "Codelist") {
    return null;
  }

  const renderTextFormats = () => (
    <div className="flex w-full items-center gap-6">
      <div className="flex-1 space-y-2">
        <Label htmlFor="config.maxLength">Maximum Character Length</Label>
        <InputField
          id="config.maxLength"
          name="config.maxLength"
          type="number"
          placeholder="Enter max length"
        />
      </div>
      <div className="flex flex-1 items-center space-x-3 pt-8">
        <SwitchField name="config.allowSpecialCharacters" />
        <Label htmlFor="config.allowSpecialCharacters">
          Allow Special Characters
        </Label>
      </div>
    </div>
  );

  const renderDateFormats = () => (
    <div className="w-1/3 space-y-2">
      <Label htmlFor="config.dateFormat">Date Format</Label>
      <Select
        id="config.dateFormat"
        name="config.dateFormat"
        placeholder="Select date format"
        options={dateFormatOptions}
      />
    </div>
  );

  const renderDateTimeFormats = () => (
    <div className="w-1/3 space-y-2">
      <Label
        htmlFor="config.dateFormat"
        className="block text-sm font-medium text-gray-700"
      >
        Date Format
      </Label>
      <Select
        id="config.dateFormat"
        name="config.dateFormat"
        placeholder="Select date format"
        options={dateTimeFormatOptions}
      />
    </div>
  );

  const renderNumberFormats = () => (
    <div className="flex gap-5">
      <div className="w-1/3 space-y-2">
        <Label
          htmlFor="config.minValue"
          className="block text-sm font-medium text-gray-700"
        >
          Minimum Value
        </Label>
        <InputField
          id="config.minValue"
          name="config.minValue"
          type="number"
          placeholder="Enter minimum value"
        />
      </div>
      <div className="w-1/3 space-y-2">
        <Label
          htmlFor="config.maxValue"
          className="block text-sm font-medium text-gray-700"
        >
          Maximum Value
        </Label>
        <InputField
          id="config.maxValue"
          name="config.maxValue"
          type="number"
          placeholder="Enter maximum value"
        />
      </div>
    </div>
  );

  const renderDecimalFormats = () => (
    <div className="flex items-center gap-5">
      <div className="flex-1 space-y-2">
        <Label
          htmlFor="config.minValue"
          className="block text-sm font-medium text-gray-700"
        >
          Minimum Value
        </Label>
        <InputField
          id="config.minValue"
          name="config.minValue"
          type="number"
          step="0.01"
          placeholder="Enter minimum value"
        />
      </div>
      <div className="flex-1 space-y-2">
        <Label
          htmlFor="config.maxValue"
          className="block text-sm font-medium text-gray-700"
        >
          Maximum Value
        </Label>
        <InputField
          id="config.maxValue"
          name="config.maxValue"
          type="number"
          step="0.01"
          placeholder="Enter maximum value"
        />
      </div>

      <div className="flex-1 space-y-2">
        <Label
          htmlFor="config.decimalPlaces"
          className="block text-sm font-medium text-gray-700"
        >
          Decimal Places
        </Label>
        <InputField
          id="config.decimalPlaces"
          name="config.decimalPlaces"
          type="number"
          min="0"
          max="10"
          placeholder="Enter decimal places"
        />
      </div>

      <div className="flex flex-1 items-center space-x-3 pt-7">
        <Label
          htmlFor="config.fixedDecimalPlaces"
          className="text-sm font-medium text-gray-700"
        >
          Fixed Decimal Places
        </Label>
        <SwitchField
          id="config.fixedDecimalPlaces"
          name="config.fixedDecimalPlaces"
        />
      </div>

      <div className="flex flex-1 items-center space-x-3 pt-7">
        <Label
          htmlFor="config.allowTrailingZeros"
          className="text-sm font-medium text-gray-700"
        >
          Allow Trailing Zeros
        </Label>
        <SwitchField
          id="config.allowTrailingZeros"
          name="config.allowTrailingZeros"
        />
      </div>

      <div className="flex flex-1 items-center space-x-3 pt-7">
        <Label
          htmlFor="config.allowScientificNotation"
          className="text-sm font-medium text-gray-700"
        >
          Allow Scientific Notation
        </Label>
        <SwitchField
          id="config.allowScientificNotation"
          name="config.allowScientificNotation"
        />
      </div>
    </div>
  );

  return (
    <div className="space-y-6 rounded-xl border border-t border-gray-200 p-5 pt-6 shadow-lg">
      <h3 className="text-lg font-semibold text-gray-900">Format</h3>

      {(fieldType === "Text" || fieldType === "TextArea") &&
        renderTextFormats()}
      {fieldType === "Date" && renderDateFormats()}
      {fieldType === "DateTime" && renderDateTimeFormats()}
      {fieldType === "Integer" && renderNumberFormats()}
      {fieldType === "Decimal" && renderDecimalFormats()}
    </div>
  );
};
