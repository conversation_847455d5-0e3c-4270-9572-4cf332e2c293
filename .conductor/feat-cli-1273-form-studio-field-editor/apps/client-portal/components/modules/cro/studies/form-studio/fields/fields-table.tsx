"use client";

import { Plus } from "lucide-react";

import { EmptyState } from "@/components/shared/empty-state";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { usePermissions, useRole } from "@/hooks/auth";
import { UserType } from "@/lib/apis/auth/types";
import type { FieldLibraryItem } from "@/lib/apis/field-library/types";

import { getColumns } from "./columns";

type FieldLibraryTableProps = {
  data: FieldLibraryItem[];
  isLoading: boolean;
  showAddModal: boolean;
  setShowAddModal: (show: boolean) => void;
  onOpenEdit: (id: string) => void;
};

export const FieldLibraryTable = ({
  data,
  isLoading,
  setShowAddModal,
  onOpenEdit,
}: FieldLibraryTableProps) => {
  const { can } = usePermissions();
  const canCreateField = can("create:own", "studies");
  const { is } = useRole();
  const isCRO = is(UserType.SPONSOR);

  const columns = getColumns({ onEdit: onOpenEdit });

  if (isLoading) {
    return (
      <TableLoading
        columns={columns}
        length={10}
        headCellStyle="text-gray-500 p-4"
        enableSorting
      />
    );
  }

  return (
    <Table
      columns={columns}
      data={data}
      headCellStyle="text-gray-500 p-4"
      enableSorting
      isLoading={isLoading}
      customEmptyCard={
        <EmptyState
          isShowNewButton={isCRO && canCreateField}
          title="No fields added yet"
          onNew={() => setShowAddModal(true)}
          icon={<EmptyFieldLibraryIcon />}
          iconNew={<Plus size={20} />}
          newLabel="Add New Field"
          description={
            <>
              <p>Start building your form by adding your first field.</p>
              <p>
                Once a field is added, you&apos;ll be able to configure
                validation, metadata, and more.
              </p>
            </>
          }
        />
      }
    />
  );
};

export const EmptyFieldLibraryIcon = () => (
  <svg
    width="54"
    height="54"
    viewBox="0 0 54 54"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="1.5"
      y="2"
      width="51"
      height="51"
      rx="25.5"
      stroke="#7200FF"
      strokeWidth="3"
    />
    <path
      d="M16 18H38M16 27H38M16 36H28"
      stroke="#7200FF"
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect x="30" y="30" width="24" height="24" rx="12" fill="#7200FF" />
    <path d="M37 42H47H37Z" fill="#7200FF" />
    <path
      d="M37 42H47"
      stroke="white"
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M42 37V47"
      stroke="white"
      strokeWidth="3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
