import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { parseAsInteger, parseAsString, useQueryStates } from "nuqs";
import toast from "react-hot-toast";

import { fieldLibraryApi } from "@/lib/apis/field-library";
import type { CreateFieldLibraryDto } from "@/lib/apis/field-library/types";
import { MetadataParams } from "@/lib/apis/types";

export const useFieldLibrary = (studyId?: string) => {
  const [params] = useQueryStates({
    page: parseAsInteger.withDefault(1),
    limit: parseAsInteger.withDefault(25),
    search: parseAsString,
    status: parseAsString,
    fieldType: parseAsString,
    fieldName: parseAsString,
    orderBy: parseAsString.withDefault(""),
    orderDirection: parseAsString.withDefault("desc") as any,
  });

  const queryParams: MetadataParams = {
    page: params.page,
    take: params.limit,
    orderBy: params.orderBy,
    orderDirection: params.orderDirection,
    filter: {
      search: params.search ?? undefined,
      status: params.status ?? undefined,
      fieldType: params.fieldType ?? undefined,
      fieldName: params.fieldName ?? undefined,
      studyId,
    },
  };

  return useQuery({
    queryKey: ["field-library", queryParams],
    queryFn: () => fieldLibraryApi.getFields(queryParams),
    enabled: !!studyId,
    placeholderData: (prev) => prev,
  });
};

export const useCreateField = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFieldLibraryDto) =>
      fieldLibraryApi.createField(data),
    onSuccess: () => {
      toast.success("Field created successfully");
      queryClient.invalidateQueries({ queryKey: ["field-library"] });
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to create field");
    },
  });
};
