"use client";

import { Card } from "flowbite-react";
import { useParams, usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { FormStudioFieldsBreadcrumb } from "@/components/breadcrumb";
import { TableDataPagination } from "@/components/ui/pagination";
import { useStudyStore } from "@/stores/study-store";

import { AddFieldModal } from "./components/add-field-modal";
import { FieldLibraryTable } from "./fields-table";
import { FieldLibraryHeading } from "./heading";
import { useFieldLibrary } from "./hooks/use-field-library";

export const FieldLibraryContent = () => {
  const { currentStudy } = useStudyStore();
  const { data, isLoading } = useFieldLibrary(currentStudy?.id);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editFieldId, setEditFieldId] = useState<string | null>(null);
  const pathname = usePathname();
  const router = useRouter();
  const { id } = useParams();

  useEffect(() => {
    if (currentStudy && !pathname.includes(currentStudy.id)) {
      const newPath = pathname.replace(id as string, currentStudy.id);
      router.replace(newPath);
    }
  }, [currentStudy, pathname, router, id]);

  const handleOpenEdit = (fieldId: string) => {
    setEditFieldId(fieldId);
    setShowAddModal(true);
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditFieldId(null);
  };

  return (
    <div className="flex h-full flex-col gap-2.5">
      <FormStudioFieldsBreadcrumb />
      <Card className="flex-1 overflow-hidden [&>div]:p-0">
        <FieldLibraryHeading
          showAddModal={showAddModal}
          setShowAddModal={setShowAddModal}
        />
        <FieldLibraryTable
          data={data?.results ?? []}
          isLoading={isLoading}
          showAddModal={showAddModal}
          setShowAddModal={setShowAddModal}
          onOpenEdit={handleOpenEdit}
        />
        {data?.metadata && data?.results.length > 0 && (
          <div className="p-4">
            <TableDataPagination
              metadata={data.metadata}
              additionalLabel="fields"
            />
          </div>
        )}
      </Card>
      <AddFieldModal
        show={showAddModal}
        onClose={handleCloseModal}
        mode={editFieldId ? "edit" : "add"}
        fieldId={editFieldId ?? undefined}
      />
    </div>
  );
};
