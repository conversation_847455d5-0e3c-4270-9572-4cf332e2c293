"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
} from "@/components/ui/modal";
import type { CreateFieldLibraryDto } from "@/lib/apis/field-library/types";
import { cn } from "@/lib/utils";
import { useStudyStore } from "@/stores/study-store";
import { customTheme } from "@/theme";

import {
  useCreateField,
  useFieldDetail,
  useUpdateField,
} from "../hooks/use-field-library";
import { FieldDetailsSection } from "./add-field-modal/field-details-section";
import { FormatSection } from "./add-field-modal/format-section";
import {
  defaultConfigForType,
  type FieldType,
  formSchema,
  type FormValues,
} from "./add-field-modal/schema";

type AddFieldModalProps = {
  show: boolean;
  onClose: () => void;
  mode?: "add" | "edit";
  fieldId?: string;
};

const fieldTypeOptions = [
  { label: "Text", value: "Text" },
  { label: "TextArea", value: "TextArea" },
  { label: "Date", value: "Date" },
  { label: "DateTime", value: "DateTime" },
  { label: "Integer", value: "Integer" },
  { label: "Decimal", value: "Decimal" },
  { label: "Codelist", value: "Codelist" },
];

// For now, empty codelist options - to be populated with actual code lists
const codelistOptions: { label: string; value: string }[] = [];

export const AddFieldModal = ({
  show,
  onClose,
  mode = "add",
  fieldId,
}: AddFieldModalProps) => {
  const { currentStudy } = useStudyStore();
  const isEdit = mode === "edit" && !!fieldId;
  const createMutation = useCreateField();
  const updateMutation = useUpdateField();
  const { data: fieldDetail, isLoading: isLoadingDetail } = useFieldDetail(
    isEdit ? fieldId : undefined,
  );
  const hasDetailError = isEdit && !isLoadingDetail && !fieldDetail;

  const isPending = isEdit
    ? updateMutation.isPending
    : createMutation.isPending;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      // Start with no type selected; cast to satisfy union input
      fieldType: "",
      displayLabel: "",
      fieldName: "",
      shortCode: "",
      description: "",
      cdashTarget: "",
      sdtmTarget: "",
      codelist: "",
      // Start with empty config; we will populate relevant keys when fieldType changes
      config: {},
    } as unknown as FormValues,
  });

  // Auto-generate shortcode from display label (fieldName is now manual)
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "displayLabel" && value.displayLabel) {
        // Shortcode: UPPER_SNAKE_CASE
        const shortCode = value.displayLabel
          .toUpperCase()
          .replace(/[^A-Z0-9]/g, "_")
          .replace(/_+/g, "_")
          .replace(/^_|_$/g, "");
        form.setValue("shortCode", shortCode, { shouldValidate: true });
      }

      if (name === "fieldType") {
        const ft = value.fieldType as FieldType;
        // Reset config to only relevant keys when field type changes
        form.setValue("config", defaultConfigForType(ft) as any, {
          shouldValidate: false,
        });
        if (ft !== "Codelist") {
          form.setValue("codelist", "", { shouldValidate: false });
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Populate form in edit mode when field details are loaded
  useEffect(() => {
    if (show && isEdit && fieldDetail) {
      const ft = fieldDetail.fieldType as FieldType;
      const configDefaults = defaultConfigForType(ft);
      const mergedConfig = {
        ...configDefaults,
        ...(fieldDetail.config || {}),
      } as any;
      const values = {
        fieldType: ft,
        displayLabel: fieldDetail.displayLabel || "",
        fieldName: fieldDetail.fieldName || "",
        shortCode: fieldDetail.shortCode || "",
        description: "",
        cdashTarget: fieldDetail.metadata?.cdash?.variableName || "",
        sdtmTarget:
          (fieldDetail.metadata as any)?.sdtm?.variableName ||
          (fieldDetail.metadata as any)?.sdtm?.variable ||
          "",
        codelist: (fieldDetail.config as any)?.codelist || "",
        config: mergedConfig,
      } as unknown as FormValues;
      form.reset(values);
    }
  }, [show, isEdit, fieldDetail, form]);

  const onSubmit = async (values: FormValues) => {
    if (!currentStudy?.id) {
      return;
    }

    try {
      // Config is already properly typed/filtered by the schema
      // RHF stores numbers as strings; schema coercion handled it for us
      const config = (values as any).config ?? {};

      // Build metadata object
      const metadata: any = {};
      if (values.cdashTarget) {
        metadata.cdash = { variableName: values.cdashTarget };
      }
      if (values.sdtmTarget) {
        // Accept either 'variable' or 'variableName' based on backend
        metadata.sdtm = { variableName: values.sdtmTarget } as any;
      }

      if (isEdit && fieldId) {
        const updateData = {
          fieldName: values.fieldName,
          fieldType: values.fieldType,
          displayLabel: values.displayLabel,
          shortCode: values.shortCode,
          config,
          metadata: Object.keys(metadata).length > 0 ? metadata : undefined,
        };
        await updateMutation.mutateAsync({ fieldId, data: updateData });
      } else {
        const createFieldData: CreateFieldLibraryDto = {
          fieldName: values.fieldName,
          fieldType: values.fieldType,
          displayLabel: values.displayLabel,
          config,
          metadata: Object.keys(metadata).length > 0 ? metadata : undefined,
          studyId: currentStudy.id,
        };
        await createMutation.mutateAsync(createFieldData);
      }

      form.reset();
      onClose();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to submit field:", error);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      show={show}
      onClose={handleClose}
      size="7xl"
      theme={{
        ...customTheme.modal,
        content: {
          base: cn(
            customTheme.modal?.content?.base,
            "min-h-[95vh] min-w-[95vw] [&>div]:min-h-[95vh] [&>div]:min-w-[95vw]",
          ),
          ...customTheme.modal?.content,
        },
      }}
    >
      <ModalHeader>Field Editor</ModalHeader>
      <FormProvider {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-1 flex-col overflow-auto"
        >
          <ModalBody className="flex-1 space-y-6 overflow-visible">
            {isEdit && isLoadingDetail ? (
              <div className="p-4 text-sm text-gray-500">
                Loading field details...
              </div>
            ) : hasDetailError ? (
              <div className="p-4 text-sm text-red-600">
                Failed to load field details. Please try again.
              </div>
            ) : (
              <>
                <FieldDetailsSection
                  fieldTypeOptions={fieldTypeOptions}
                  codelistOptions={codelistOptions}
                />
                <FormatSection />
              </>
            )}
          </ModalBody>
          <ModalFooter>
            <div className="flex w-full justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isPending}
                disabled={isPending || (isEdit && isLoadingDetail)}
              >
                {isEdit ? "Save Changes" : "Create Field"}
              </Button>
            </div>
          </ModalFooter>
        </form>
      </FormProvider>
    </Modal>
  );
};
