"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
} from "@/components/ui/modal";
import type { CreateFieldLibraryDto } from "@/lib/apis/field-library/types";
import { cn } from "@/lib/utils";
import { useStudyStore } from "@/stores/study-store";
import { customTheme } from "@/theme";

import { useCreateField } from "../hooks/use-field-library";
import { FieldDetailsSection } from "./add-field-modal/field-details-section";
import { FormatSection } from "./add-field-modal/format-section";
import {
  defaultConfigForType,
  type FieldType,
  formSchema,
  type FormValues,
} from "./add-field-modal/schema";

type AddFieldModalProps = {
  show: boolean;
  onClose: () => void;
};

const fieldTypeOptions = [
  { label: "Text", value: "Text" },
  { label: "TextArea", value: "TextArea" },
  { label: "Date", value: "Date" },
  { label: "DateTime", value: "DateTime" },
  { label: "Integer", value: "Integer" },
  { label: "Decimal", value: "Decimal" },
  { label: "Codelist", value: "Codelist" },
];

// For now, empty codelist options - to be populated with actual code lists
const codelistOptions: { label: string; value: string }[] = [];

export const AddFieldModal = ({ show, onClose }: AddFieldModalProps) => {
  const { currentStudy } = useStudyStore();
  const { isPending } = useCreateField();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      // Start with no type selected; cast to satisfy union input
      fieldType: "",
      displayLabel: "",
      fieldName: "",
      shortCode: "",
      description: "",
      cdashTarget: "",
      sdtmTarget: "",
      codelist: "",
      // Start with empty config; we will populate relevant keys when fieldType changes
      config: {},
    } as unknown as FormValues,
  });

  // Auto-generate shortcode from display label (fieldName is now manual)
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "displayLabel" && value.displayLabel) {
        // Shortcode: UPPER_SNAKE_CASE
        const shortCode = value.displayLabel
          .toUpperCase()
          .replace(/[^A-Z0-9]/g, "_")
          .replace(/_+/g, "_")
          .replace(/^_|_$/g, "");
        form.setValue("shortCode", shortCode, { shouldValidate: true });
      }

      if (name === "fieldType") {
        const ft = value.fieldType as FieldType;
        // Reset config to only relevant keys when field type changes
        form.setValue("config", defaultConfigForType(ft) as any, {
          shouldValidate: false,
        });
        if (ft !== "Codelist") {
          form.setValue("codelist", "", { shouldValidate: false });
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const onSubmit = async (values: FormValues) => {
    if (!currentStudy?.id) {
      return;
    }

    try {
      // Config is already properly typed/filtered by the schema
      // RHF stores numbers as strings; schema coercion handled it for us
      const config = (values as any).config ?? {};

      // Build metadata object
      const metadata: any = {};
      if (values.cdashTarget) {
        metadata.cdash = { variableName: values.cdashTarget };
      }
      if (values.sdtmTarget) {
        metadata.sdtm = { variableName: values.sdtmTarget };
      }

      const createFieldData: CreateFieldLibraryDto = {
        fieldName: values.fieldName,
        fieldType: values.fieldType,
        displayLabel: values.displayLabel,
        config,
        metadata: Object.keys(metadata).length > 0 ? metadata : undefined,
        studyId: currentStudy.id,
      };
      console.log("🚀 ~ onSubmit ~ createFieldData:", createFieldData);

      // await createField(createFieldData);
      form.reset();
      onClose();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to create field:", error);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      show={show}
      onClose={handleClose}
      size="7xl"
      // className="[&_div]:min-w-screen [&_div]:min-h-screen"
      theme={{
        ...customTheme.modal,
        content: {
          base: cn(
            customTheme.modal?.content?.base,
            "min-h-[95vh] min-w-[95vw] [&>div]:min-h-[95vh] [&>div]:min-w-[95vw]",
          ),
          ...customTheme.modal?.content,
        },
      }}
    >
      <ModalHeader>Add New Field</ModalHeader>
      <FormProvider {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-1 flex-col"
        >
          <ModalBody className="flex-1 space-y-6">
            <FieldDetailsSection
              fieldTypeOptions={fieldTypeOptions}
              codelistOptions={codelistOptions}
            />
            <FormatSection />
          </ModalBody>
          <ModalFooter>
            <div className="flex w-full justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isPending}
                disabled={isPending}
              >
                Create Field
              </Button>
            </div>
          </ModalFooter>
        </form>
      </FormProvider>
    </Modal>
  );
};
