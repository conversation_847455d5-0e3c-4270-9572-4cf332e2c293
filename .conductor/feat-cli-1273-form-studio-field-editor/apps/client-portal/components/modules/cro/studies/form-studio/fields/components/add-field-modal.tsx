"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Mo<PERSON><PERSON>eader,
} from "@/components/ui/modal";
import type { CreateFieldLibraryDto } from "@/lib/apis/field-library/types";
import { cn } from "@/lib/utils";
import { useStudyStore } from "@/stores/study-store";
import { customTheme } from "@/theme";

import { useCreateField } from "../hooks/use-field-library";
import { FieldDetailsSection } from "./add-field-modal/field-details-section";
import { FormatSection } from "./add-field-modal/format-section";

type AddFieldModalProps = {
  show: boolean;
  onClose: () => void;
};

const fieldTypeOptions = [
  { label: "Text", value: "Text" },
  { label: "TextArea", value: "TextArea" },
  { label: "Date", value: "Date" },
  { label: "DateTime", value: "DateTime" },
  { label: "Integer", value: "Integer" },
  { label: "Decimal", value: "Decimal" },
  { label: "Codelist", value: "Codelist" },
];

// For now, empty codelist options - to be populated with actual code lists
const codelistOptions: { label: string; value: string }[] = [];

const formSchema = z.object({
  fieldType: z.string().min(1, "Field type is required"),
  displayLabel: z.string().min(1, "Display label is required"),
  fieldName: z
    .string()
    .min(1, "Field name is required")
    .regex(
      /^[a-zA-Z][a-zA-Z0-9_]*$/,
      "Field name must start with a letter and contain only letters, numbers, and underscores",
    ),
  shortCode: z.string().min(1, "Shortcode is required"),
  description: z.string().optional(),
  cdashTarget: z.string().optional(),
  sdtmTarget: z.string().optional(),
  codelist: z.string().optional(),
  // Format section fields nested under config
  config: z
    .object({
      maxLength: z.string().optional(),
      allowSpecialCharacters: z.boolean().optional(),
      dateFormat: z.string().optional(),
      minValue: z.string().optional(),
      maxValue: z.string().optional(),
      decimalPlaces: z.string().optional(),
      fixedDecimalPlaces: z.boolean().optional(),
      allowTrailingZeros: z.boolean().optional(),
      allowScientificNotation: z.boolean().optional(),
    })
    .optional(),
});

type FormValues = z.infer<typeof formSchema>;

export const AddFieldModal = ({ show, onClose }: AddFieldModalProps) => {
  const { currentStudy } = useStudyStore();
  const { mutateAsync: createField, isPending } = useCreateField();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fieldType: "",
      displayLabel: "",
      fieldName: "",
      shortCode: "",
      description: "",
      cdashTarget: "",
      sdtmTarget: "",
      codelist: "",
      config: {
        maxLength: "",
        allowSpecialCharacters: false,
        dateFormat: "",
        minValue: "",
        maxValue: "",
        decimalPlaces: "",
        fixedDecimalPlaces: false,
        allowTrailingZeros: false,
        allowScientificNotation: false,
      },
    },
  });

  // Auto-generate shortcode and fieldName from display label
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "displayLabel" && value.displayLabel) {
        // Shortcode: UPPER_SNAKE_CASE
        const shortCode = value.displayLabel
          .toUpperCase()
          .replace(/[^A-Z0-9]/g, "_")
          .replace(/_+/g, "_")
          .replace(/^_|_$/g, "");
        form.setValue("shortCode", shortCode, { shouldValidate: true });

        // fieldName: ensure it starts with a letter and contains only letters, numbers, and underscores
        let fieldName = value.displayLabel
          .replace(/[^a-zA-Z0-9_]/g, "_")
          .replace(/_+/g, "_")
          .replace(/^_|_$/g, "");
        if (!/^[A-Za-z]/.test(fieldName)) {
          fieldName = `F_${fieldName}`;
        }
        form.setValue("fieldName", fieldName, { shouldValidate: true });
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const onSubmit = async (values: FormValues) => {
    if (!currentStudy?.id) {
      return;
    }

    try {
      // Build config object based on field type, converting string values to appropriate types
      const config: any = {};

      if (values.config) {
        // Convert string values to numbers where needed
        if (values.config.maxLength)
          config.maxLength = parseInt(values.config.maxLength);
        if (typeof values.config.allowSpecialCharacters === "boolean") {
          config.allowSpecialCharacters = values.config.allowSpecialCharacters;
        }
        if (values.config.dateFormat)
          config.dateFormat = values.config.dateFormat;
        if (values.config.minValue) {
          config.minValue =
            values.fieldType === "Integer"
              ? parseInt(values.config.minValue)
              : parseFloat(values.config.minValue);
        }
        if (values.config.maxValue) {
          config.maxValue =
            values.fieldType === "Integer"
              ? parseInt(values.config.maxValue)
              : parseFloat(values.config.maxValue);
        }
        if (values.config.decimalPlaces)
          config.decimalPlaces = parseInt(values.config.decimalPlaces);
        if (typeof values.config.fixedDecimalPlaces === "boolean") {
          config.fixedDecimalPlaces = values.config.fixedDecimalPlaces;
        }
        if (typeof values.config.allowTrailingZeros === "boolean") {
          config.allowTrailingZeros = values.config.allowTrailingZeros;
        }
        if (typeof values.config.allowScientificNotation === "boolean") {
          config.allowScientificNotation =
            values.config.allowScientificNotation;
        }
      }

      // Build metadata object
      const metadata: any = {};
      if (values.cdashTarget) {
        metadata.cdash = { target: values.cdashTarget };
      }
      if (values.sdtmTarget) {
        metadata.sdtm = { target: values.sdtmTarget };
      }

      const createFieldData: CreateFieldLibraryDto = {
        fieldName: values.fieldName,
        fieldType: values.fieldType,
        displayLabel: values.displayLabel,
        config,
        metadata: Object.keys(metadata).length > 0 ? metadata : undefined,
        studyId: currentStudy.id,
      };
      console.log("🚀 ~ onSubmit ~ createFieldData:", createFieldData);

      // await createField(createFieldData);
      form.reset();
      onClose();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to create field:", error);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      show={show}
      onClose={handleClose}
      size="7xl"
      // className="[&_div]:min-w-screen [&_div]:min-h-screen"
      theme={{
        ...customTheme.modal,
        content: {
          base: cn(
            customTheme.modal?.content?.base,
            "min-h-[95vh] min-w-[95vw] [&>div]:min-h-[95vh] [&>div]:min-w-[95vw]",
          ),
          ...customTheme.modal?.content,
        },
      }}
    >
      <ModalHeader>Add New Field</ModalHeader>
      <FormProvider {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-1 flex-col"
        >
          <ModalBody className="flex-1 space-y-6">
            <FieldDetailsSection
              fieldTypeOptions={fieldTypeOptions}
              codelistOptions={codelistOptions}
            />
            <FormatSection />
          </ModalBody>
          <ModalFooter>
            <div className="flex w-full justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                isLoading={isPending}
                disabled={isPending}
              >
                Create Field
              </Button>
            </div>
          </ModalFooter>
        </form>
      </FormProvider>
    </Modal>
  );
};
